NEXT_PUBLIC_SUPABASE_URL=https://********************.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=****************************************************************************************************************************************************************************************************************
   
NEXT_PUBLIC_OPENAI_API_KEY=sk-************************************************ 
NEXT_PUBLIC_OPENAI_ASSISTANT_KEY=asst_************************

# Update these with your Supabase details from your project settings > API 
 SUPABASE_SERVICE_ROLE_KEY=***************************************************************************************************************************************************************************************************************************

# Update these with your Stripe credentials from https://dashboard.stripe.com/apikeys
# NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_***************************************************************************************************
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_***************************************************************************************************
# STRIPE_SECRET_KEY=sk_live_*************************************************************************************************** 
STRIPE_SECRET_KEY=sk_test_*************************************************************************************************** 
# The commented variable is usually for production webhook key. This you get in the Stripe dashboard and is usually shorter.
# STRIPE_WEBHOOK_SECRET=whsec_********************************
STRIPE_WEBHOOK_SECRET=whsec_****************************************************************

# Update this with your stable site URL only for the production environment.
# NEXT_PUBLIC_SITE_URL=https://horizon-ui.com/shadcn-nextjs-boilerplate
# NEXT_PUBLIC_SITE_URL=https://******************.com

NEXT_PUBLIC_AWS_S3_REGION=eu-north-1 
NEXT_PUBLIC_AWS_S3_ACCESS_KEY_ID=******************** 
NEXT_PUBLIC_AWS_S3_SECRET_ACCESS_KEY=**************************************** 
NEXT_PUBLIC_AWS_S3_BUCKET_NAME=mybucket