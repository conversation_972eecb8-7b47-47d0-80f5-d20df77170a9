'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  HiOutlineMagnifyingGlass, 
  HiOutlineFilter,
  HiOutlineEye,
  HiOutlineArrowDownTray,
  HiOutlineClock,
  HiOutlineChartBarSquare
} from 'react-icons/hi2';

interface Dataset {
  id: string;
  title: string;
  category: string;
  description: string;
  lastUpdated: string;
  recordCount: number;
  tier: 'Free' | 'Pro' | 'Enterprise';
  format: string[];
  tags: string[];
  popularity: number;
}

const sampleDatasets: Dataset[] = [
  {
    id: '1',
    title: 'Ulaanbaatar Real Estate Prices Q4 2024',
    category: 'Real Estate',
    description: 'Comprehensive property prices across all districts including apartments, houses, and commercial properties.',
    lastUpdated: '2 hours ago',
    recordCount: 15420,
    tier: 'Pro',
    format: ['CSV', 'JSON', 'Excel'],
    tags: ['real-estate', 'ulaanbaatar', 'prices', 'property'],
    popularity: 95
  },
  {
    id: '2',
    title: 'Coffee Shop Market Analysis Mongolia',
    category: 'F&B',
    description: 'Consumer preferences, pricing strategies, and market saturation analysis for coffee businesses.',
    lastUpdated: '1 day ago',
    recordCount: 892,
    tier: 'Free',
    format: ['CSV', 'JSON'],
    tags: ['coffee', 'market-analysis', 'consumer-behavior'],
    popularity: 78
  },
  {
    id: '3',
    title: 'Retail Foot Traffic Patterns 2024',
    category: 'Retail',
    description: 'Shopping center and street-level traffic analytics with seasonal patterns and peak hours.',
    lastUpdated: '3 days ago',
    recordCount: 45600,
    tier: 'Enterprise',
    format: ['CSV', 'JSON', 'Excel', 'API'],
    tags: ['retail', 'foot-traffic', 'analytics', 'seasonal'],
    popularity: 87
  },
  {
    id: '4',
    title: 'Demographics by Aimag 2024',
    category: 'Demographics',
    description: 'Population distribution, age groups, income levels, and economic indicators by region.',
    lastUpdated: '1 week ago',
    recordCount: 3200,
    tier: 'Pro',
    format: ['CSV', 'JSON', 'Excel'],
    tags: ['demographics', 'population', 'aimag', 'economics'],
    popularity: 92
  },
  {
    id: '5',
    title: 'Transportation Usage Patterns',
    category: 'Transportation',
    description: 'Public transport usage, ride-sharing data, and traffic flow analysis for urban planning.',
    lastUpdated: '5 days ago',
    recordCount: 28900,
    tier: 'Enterprise',
    format: ['CSV', 'JSON', 'API'],
    tags: ['transportation', 'public-transport', 'traffic', 'urban-planning'],
    popularity: 73
  },
  {
    id: '6',
    title: 'Small Business Registration Trends',
    category: 'Business',
    description: 'New business registrations, industry distribution, and success rates across Mongolia.',
    lastUpdated: '2 weeks ago',
    recordCount: 5670,
    tier: 'Free',
    format: ['CSV', 'JSON'],
    tags: ['business', 'registration', 'startups', 'industry'],
    popularity: 65
  }
];

const categories = ['All', 'Real Estate', 'F&B', 'Retail', 'Demographics', 'Transportation', 'Business'];
const tiers = ['All', 'Free', 'Pro', 'Enterprise'];

function DatasetCard({ dataset }: { dataset: Dataset }) {
  const tierColors = {
    Free: 'bg-green-100 text-green-800',
    Pro: 'bg-blue-100 text-blue-800',
    Enterprise: 'bg-purple-100 text-purple-800'
  };

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold text-gray-900 mb-2">
              {dataset.title}
            </CardTitle>
            <p className="text-sm text-gray-600 mb-3">{dataset.description}</p>
            <div className="flex flex-wrap gap-1 mb-2">
              {dataset.tags.map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
          <Badge className={`ml-2 ${tierColors[dataset.tier]}`}>
            {dataset.tier}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between mb-4 text-sm text-gray-500">
          <div className="flex items-center space-x-4">
            <span className="flex items-center">
              <HiOutlineClock className="h-4 w-4 mr-1" />
              {dataset.lastUpdated}
            </span>
            <span>{dataset.recordCount.toLocaleString()} records</span>
          </div>
          <div className="flex items-center">
            <HiOutlineChartBarSquare className="h-4 w-4 mr-1" />
            <span>{dataset.popularity}% popular</span>
          </div>
        </div>
        
        <div className="flex items-center justify-between mb-4">
          <div className="flex space-x-1">
            {dataset.format.map((format, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {format}
              </Badge>
            ))}
          </div>
          <Badge variant="outline" className="text-xs">
            {dataset.category}
          </Badge>
        </div>
        
        <div className="flex space-x-2">
          <Button size="sm" variant="outline" className="flex-1">
            <HiOutlineEye className="h-4 w-4 mr-1" />
            Preview
          </Button>
          <Button size="sm" className="flex-1 bg-gradient-to-r from-oyu-blue-600 to-oyu-purple-600">
            <HiOutlineArrowDownTray className="h-4 w-4 mr-1" />
            Access
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

export default function DatasetManager() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedTier, setSelectedTier] = useState('All');

  const filteredDatasets = sampleDatasets.filter(dataset => {
    const matchesSearch = dataset.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         dataset.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         dataset.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'All' || dataset.category === selectedCategory;
    const matchesTier = selectedTier === 'All' || dataset.tier === selectedTier;
    
    return matchesSearch && matchesCategory && matchesTier;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dataset Explorer</h1>
          <p className="text-gray-600 mt-1">
            Discover and access curated datasets for Mongolian market intelligence
          </p>
        </div>
        <Button className="bg-gradient-to-r from-oyu-blue-600 to-oyu-purple-600">
          Request Custom Dataset
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <HiOutlineMagnifyingGlass className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search datasets, tags, or descriptions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-40">
                  <HiOutlineFilter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={selectedTier} onValueChange={setSelectedTier}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Tier" />
                </SelectTrigger>
                <SelectContent>
                  {tiers.map(tier => (
                    <SelectItem key={tier} value={tier}>
                      {tier}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <div className="flex items-center justify-between">
        <p className="text-gray-600">
          Showing {filteredDatasets.length} of {sampleDatasets.length} datasets
        </p>
      </div>

      {/* Dataset Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredDatasets.map((dataset) => (
          <DatasetCard key={dataset.id} dataset={dataset} />
        ))}
      </div>

      {filteredDatasets.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No datasets found matching your criteria.</p>
          <p className="text-gray-400 text-sm mt-2">Try adjusting your search or filters.</p>
        </div>
      )}
    </div>
  );
}
