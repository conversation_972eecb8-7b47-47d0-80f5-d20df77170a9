import endent from 'endent';
import {
  createParser,
  ParsedEvent,
  ReconnectInterval,
} from 'eventsource-parser';

const createPrompt = (inputMessage: string) => {
  const data = (inputMessage: string) => {
    return endent`
      You are the **Oyu-Data-AI Assistant**, a specialized AI powered by GPT-4o that helps users with data intelligence and market research for Mongolia and beyond. You are an expert in:

      **Core Expertise:**
      - Mongolian market analysis and business intelligence
      - Data interpretation and visualization insights
      - Market research methodologies and best practices
      - Business feasibility analysis and competitive intelligence
      - Real estate, retail, F&B, and demographic data analysis
      - Investment decision support and risk assessment

      **Your Role:**
      - Provide data-driven insights and recommendations
      - Help interpret datasets and market trends
      - Assist with business planning and market entry strategies
      - Explain complex data in simple, actionable terms
      - Suggest relevant datasets and research approaches

      **Communication Style:**
      - Professional yet approachable
      - Data-focused and evidence-based
      - Provide specific, actionable insights
      - Use markdown formatting for clarity
      - Include relevant examples from Mongolian market when applicable

      **Platform Context:**
      You work within the Oyu-Data-AI platform, which provides curated datasets for the Mongolian market including real estate prices, business demographics, consumer behavior, and market trends. Users range from entrepreneurs to large corporations seeking market intelligence.

      **User Query:**
      ${inputMessage}
    `;
  };

  if (inputMessage) {
    return data(inputMessage);
  }
};

export async function OpenAIStream   (
  inputMessage: string,
  model: string,
  key: string | undefined,
)  {
  const prompt = createPrompt(inputMessage);

  const system = { role: 'system', content: prompt };

  const res = await fetch(`https://api.openai.com/v1/chat/completions`, {
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${key || process.env.NEXT_PUBLIC_OPENAI_API_KEY}`,
    },
    method: 'POST',
    body: JSON.stringify({
      model,
      messages: [system],
      temperature: 0,
      stream: true,
    }),
  });

  const encoder = new TextEncoder();
  const decoder = new TextDecoder();

  if (res.status !== 200) {
    const statusText = res.statusText;
    const result = await res.body?.getReader().read();
    throw new Error(
      `OpenAI API returned an error: ${
        decoder.decode(result?.value) || statusText
      }`,
    );
  }

  const stream = new ReadableStream({
    async start(controller) {
      const onParse = (event: ParsedEvent | ReconnectInterval) => {
        if (event.type === 'event') {
          const data = event.data;

          if (data === '[DONE]') {
            controller.close();
            return;
          }

          try {
            const json = JSON.parse(data);
            const text = json.choices[0].delta.content;
            const queue = encoder.encode(text);
            controller.enqueue(queue);
          } catch (e) {
            controller.error(e);
          }
        }
      };

      const parser = createParser(onParse);

      for await (const chunk of res.body as any) {
        parser.feed(decoder.decode(chunk));
      }
    },
  });

  return stream;
};
