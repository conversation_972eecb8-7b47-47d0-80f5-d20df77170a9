import SupabaseProvider from './supabase-provider';
import { PropsWithChildren } from 'react';
import '@/styles/globals.css';
import { ThemeProvider } from './theme-provider';

export const dynamic = 'force-dynamic';

export default function RootLayout({
  // Layouts must accept a children prop.
  // This will be populated with nested layouts or pages
  children
}: PropsWithChildren) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <title>
          Oyu-Data-AI - Mongolian Market Intelligence & Data Analytics Platform
        </title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        {/* <!--  Social tags   --> */}
        <meta
          name="keywords"
          content="Mongolia market data, business intelligence, real estate analytics, consumer insights, market research, data platform, Ulaanbaatar, Mongolian economy"
        />
        <meta name="description" content="Oyu-Data-AI provides curated datasets and AI-powered insights for the Mongolian market. Access real estate trends, consumer behavior, and business intelligence to make data-driven decisions." />
        {/* <!-- Schema.org markup for Google+ --> */}
        <meta itemProp="name" content="Oyu-Data-AI - Mongolian Market Intelligence Platform" />
        <meta
          itemProp="description"
          content="Access curated datasets and AI-powered insights for the Mongolian market. Real estate trends, consumer behavior, and business intelligence."
        />
        <meta
          itemProp="image"
          content="/img/oyu-data-ai-social.png"
        />
        {/* <!-- Twitter Card data --> */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta
          name="twitter:title"
          content="Oyu-Data-AI - Mongolian Market Intelligence Platform"
        />
        <meta
          name="twitter:description"
          content="Access curated datasets and AI-powered insights for the Mongolian market. Real estate trends, consumer behavior, and business intelligence."
        />
        <meta
          name="twitter:image"
          content="/img/oyu-data-ai-social.png"
        />
        {/* <!-- Open Graph data --> */}
        <meta
          property="og:title"
          content="Oyu-Data-AI - Mongolian Market Intelligence Platform"
        />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://oyu-data-ai.com" />
        <meta
          property="og:image"
          content="/img/oyu-data-ai-social.png"
        />
        <meta
          property="og:description"
          content="Access curated datasets and AI-powered insights for the Mongolian market. Real estate trends, consumer behavior, and business intelligence."
        />
        <meta
          property="og:site_name"
          content="Oyu-Data-AI"
        />
        <link rel="canonical" href="https://oyu-data-ai.com" />
        <link rel="icon" href="/img/favicon.ico" />
      </head>
      <body id={'root'} className="loading bg-white">
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <SupabaseProvider>
            <main id="skip">{children}</main>
          </SupabaseProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
