'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON><PERSON>, HiX } from 'react-icons/hi2';

interface SubscriptionTier {
  name: string;
  price: string;
  period: string;
  description: string;
  features: {
    name: string;
    included: boolean;
    limit?: string;
  }[];
  popular?: boolean;
  buttonText: string;
  buttonVariant: 'outline' | 'default';
}

const subscriptionTiers: SubscriptionTier[] = [
  {
    name: 'Freemium',
    price: 'Free',
    period: 'forever',
    description: 'Perfect for researchers and students exploring Mongolian market data',
    features: [
      { name: 'Basic datasets access', included: true, limit: '5 datasets/month' },
      { name: 'AI assistant queries', included: true, limit: '10 queries/day' },
      { name: 'Data export (CSV)', included: true, limit: '100 records/export' },
      { name: 'Market insights reports', included: false },
      { name: 'API access', included: false },
      { name: 'Custom research requests', included: false },
      { name: 'Priority support', included: false }
    ],
    buttonText: 'Get Started',
    buttonVariant: 'outline'
  },
  {
    name: 'Pro',
    price: '$99',
    period: 'per month',
    description: 'Ideal for entrepreneurs and small businesses making data-driven decisions',
    features: [
      { name: 'Full datasets access', included: true, limit: 'Most datasets' },
      { name: 'AI assistant queries', included: true, limit: 'Unlimited' },
      { name: 'Data export (CSV, JSON)', included: true, limit: 'Unlimited' },
      { name: 'Market insights reports', included: true, limit: 'Weekly reports' },
      { name: 'API access', included: true, limit: '1,000 calls/month' },
      { name: 'Custom research requests', included: true, limit: '2 requests/month' },
      { name: 'Priority support', included: false }
    ],
    popular: true,
    buttonText: 'Start Pro Trial',
    buttonVariant: 'default'
  },
  {
    name: 'Enterprise',
    price: '$499',
    period: 'per month',
    description: 'For large corporations requiring comprehensive market intelligence',
    features: [
      { name: 'All datasets access', included: true, limit: 'Complete access' },
      { name: 'AI assistant queries', included: true, limit: 'Unlimited' },
      { name: 'Data export (All formats)', included: true, limit: 'Unlimited' },
      { name: 'Market insights reports', included: true, limit: 'Daily reports' },
      { name: 'API access', included: true, limit: 'Unlimited' },
      { name: 'Custom research requests', included: true, limit: 'Unlimited' },
      { name: 'Priority support', included: true, limit: '24/7 support' }
    ],
    buttonText: 'Contact Sales',
    buttonVariant: 'outline'
  }
];

function FeatureItem({ feature }: { feature: { name: string; included: boolean; limit?: string } }) {
  return (
    <div className="flex items-start space-x-3">
      <div className={`mt-0.5 ${feature.included ? 'text-green-500' : 'text-gray-300'}`}>
        {feature.included ? (
          <HiCheck className="h-5 w-5" />
        ) : (
          <HiX className="h-5 w-5" />
        )}
      </div>
      <div className="flex-1">
        <span className={`text-sm ${feature.included ? 'text-gray-900' : 'text-gray-500'}`}>
          {feature.name}
        </span>
        {feature.limit && (
          <span className="ml-2 text-xs text-gray-500">({feature.limit})</span>
        )}
      </div>
    </div>
  );
}

function TierCard({ tier }: { tier: SubscriptionTier }) {
  return (
    <Card className={`relative ${tier.popular ? 'border-oyu-blue-500 shadow-lg' : ''}`}>
      {tier.popular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <Badge className="bg-gradient-to-r from-oyu-blue-600 to-oyu-purple-600 text-white">
            Most Popular
          </Badge>
        </div>
      )}
      <CardHeader className="text-center pb-4">
        <CardTitle className="text-xl font-bold">{tier.name}</CardTitle>
        <div className="mt-2">
          <span className="text-3xl font-bold">{tier.price}</span>
          {tier.price !== 'Free' && (
            <span className="text-gray-500 ml-1">/{tier.period}</span>
          )}
        </div>
        <p className="text-sm text-gray-600 mt-2">{tier.description}</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 mb-6">
          {tier.features.map((feature, index) => (
            <FeatureItem key={index} feature={feature} />
          ))}
        </div>
        <Button 
          className={`w-full ${tier.buttonVariant === 'default' ? 'bg-gradient-to-r from-oyu-blue-600 to-oyu-purple-600 hover:from-oyu-blue-700 hover:to-oyu-purple-700' : ''}`}
          variant={tier.buttonVariant}
        >
          {tier.buttonText}
        </Button>
      </CardContent>
    </Card>
  );
}

export default function SubscriptionTiers() {
  return (
    <div className="py-8">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Choose Your Data Intelligence Plan
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Access Mongolian market data and insights tailored to your business needs.
          From startups to enterprises, we have the right plan for you.
        </p>
      </div>
      
      <div className="grid gap-8 md:grid-cols-3 max-w-6xl mx-auto">
        {subscriptionTiers.map((tier, index) => (
          <TierCard key={index} tier={tier} />
        ))}
      </div>
      
      <div className="text-center mt-8">
        <p className="text-sm text-gray-500">
          All plans include access to our AI-powered market insights and regular data updates.
          <br />
          Enterprise customers get dedicated support and custom integrations.
        </p>
      </div>
    </div>
  );
}
