{"name": "shadcn-nextjs-boilerplate", "version": "3.0.0", "private": true, "scripts": {"init": "npm install && npx shadcn@latest add --all", "dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "preinstall": "npx npm-force-resolutions", "stripe:login": "stripe login", "stripe:listen": "stripe listen --forward-to=localhost:3000/api/webhooks", "stripe:fixtures": "stripe fixtures fixtures/stripe-fixtures.json", "supabase:start": "npx supabase start", "supabase:stop": "npx supabase stop", "supabase:status": "npx supabase status", "supabase:restart": "npm run supabase:stop && npm run supabase:start", "supabase:reset": "npx supabase db reset", "supabase:link": "npx supabase link", "supabase:generate-types": "npx supabase gen types typescript --local --schema public > types_db.ts", "supabase:generate-migration": "npx supabase db diff | npx supabase migration new", "supabase:generate-seed": "npx supabase db dump --data-only -f supabase/seed.sql", "supabase:push": "npx supabase db push", "supabase:pull": "npx supabase db pull"}, "dependencies": {"@aws-sdk/client-s3": "^3.583.0", "@babel/traverse": "^7.24.6", "@codemirror/legacy-modes": "^6.4.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@heroicons/react": "^2.1.3", "@hookform/resolvers": "^3.10.0", "@material-tailwind/react": "2.1.9", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/stripe-js": "^3.4.1", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.4.0", "@supabase/supabase-js": "^2.45.1", "@tanstack/react-table": "^8.17.3", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^15.0.7", "@testing-library/user-event": "^14.5.2", "@uiw/codemirror-theme-tokyo-night": "^4.22.1", "@uiw/react-codemirror": "^4.22.1", "adblock-detect-react": "^1.3.1", "apexcharts": "3.49.1", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "crypto": "^1.0.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "endent": "^2.1.0", "eventsource-parser": "^1.1.2", "framer-motion": "^11.2.6", "input-otp": "^1.4.2", "lucide-react": "^0.379.0", "next": "^15.1.6", "next-themes": "^0.3.0", "openai": "^4.47.1", "php-serialize": "^4.1.1", "react": "^19.0.0-rc.1", "react-apexcharts": "1.4.1", "react-custom-scrollbars-2": "^4.5.0", "react-day-picker": "^9.8.1", "react-dom": "^19.0.0-rc.1", "react-github-btn": "^1.4.0", "react-github-button": "^0.1.11", "react-hook-form": "^7.62.0", "react-icons": "^5.2.1", "react-is": "^18.3.1", "react-markdown": "^9.0.1", "react-merge-refs": "^2.1.1", "react-resizable-panels": "^2.1.9", "react-router-dom": "^6.23.1", "react-to-print": "^2.15.1", "recharts": "^2.15.4", "remark-gfm": "^4.0.0", "shadcn-ui": "^0.2.3", "sonner": "^1.7.4", "stripe": "^15.8.0", "swr": "^2.2.5", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "tailwindcss-rtl": "^0.9.0", "vaul": "^0.9.9", "web-vitals": "^4.0.1", "zod": "^3.25.76"}, "resolutions": {"minimist": "1.2.6"}, "devDependencies": {"@babel/cli": "^7.24.6", "@babel/core": "^7.24.6", "@ianvs/prettier-plugin-sort-imports": "^4.2.1", "@types/node": "^20.12.12", "@types/react": "^18.3.3", "@types/react-dom": "18.3.0", "autoprefixer": "^10.4.19", "encoding": "^0.1.13", "eslint": "^9.3.0", "eslint-config-next": "^15.1.6", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-tailwindcss": "^3.17.0", "eventsource": "^2.0.2", "npm-force-resolutions": "^0.0.10", "postcss": "^8.4.38", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.14", "supabase": "^1.172.2", "tailwindcss": "^3.4.3", "typescript": "5.4.5"}, "prettier": {"arrowParens": "always", "singleQuote": true, "tabWidth": 2, "trailingComma": "none"}}