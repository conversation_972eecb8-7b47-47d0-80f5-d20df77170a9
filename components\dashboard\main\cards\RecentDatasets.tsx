'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { HiOutlineEye, HiOutlineArrowDownTray, HiOutlineClock } from 'react-icons/hi2';

interface Dataset {
  id: string;
  title: string;
  category: string;
  lastUpdated: string;
  recordCount: number;
  tier: 'Free' | 'Pro' | 'Enterprise';
  description: string;
}

const sampleDatasets: Dataset[] = [
  {
    id: '1',
    title: 'Ulaanbaatar Real Estate Prices Q4 2024',
    category: 'Real Estate',
    lastUpdated: '2 hours ago',
    recordCount: 15420,
    tier: 'Pro',
    description: 'Comprehensive property prices across all districts'
  },
  {
    id: '2',
    title: 'Coffee Shop Market Analysis Mongolia',
    category: 'F&B',
    lastUpdated: '1 day ago',
    recordCount: 892,
    tier: 'Free',
    description: 'Consumer preferences and market saturation data'
  },
  {
    id: '3',
    title: 'Retail Foot Traffic Patterns 2024',
    category: 'Retail',
    lastUpdated: '3 days ago',
    recordCount: 45600,
    tier: 'Enterprise',
    description: 'Shopping center and street-level traffic analytics'
  },
  {
    id: '4',
    title: 'Demographics by Aimag 2024',
    category: 'Demographics',
    lastUpdated: '1 week ago',
    recordCount: 3200,
    tier: 'Pro',
    description: 'Population distribution and economic indicators'
  }
];

function DatasetCard({ dataset }: { dataset: Dataset }) {
  const tierColors = {
    Free: 'bg-green-100 text-green-800',
    Pro: 'bg-blue-100 text-blue-800',
    Enterprise: 'bg-purple-100 text-purple-800'
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold text-gray-900 mb-1">
              {dataset.title}
            </CardTitle>
            <p className="text-sm text-gray-600">{dataset.description}</p>
          </div>
          <Badge className={`ml-2 ${tierColors[dataset.tier]}`}>
            {dataset.tier}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span className="flex items-center">
              <HiOutlineClock className="h-4 w-4 mr-1" />
              {dataset.lastUpdated}
            </span>
            <span>{dataset.recordCount.toLocaleString()} records</span>
          </div>
          <Badge variant="outline" className="text-xs">
            {dataset.category}
          </Badge>
        </div>
        <div className="flex space-x-2">
          <Button size="sm" variant="outline" className="flex-1">
            <HiOutlineEye className="h-4 w-4 mr-1" />
            Preview
          </Button>
          <Button size="sm" className="flex-1 bg-gradient-to-r from-oyu-blue-600 to-oyu-purple-600">
            <HiOutlineArrowDownTray className="h-4 w-4 mr-1" />
            Export
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

export default function RecentDatasets() {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl font-bold">Recent Datasets</CardTitle>
          <Button variant="outline" size="sm">
            View All
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2">
          {sampleDatasets.map((dataset) => (
            <DatasetCard key={dataset.id} dataset={dataset} />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
