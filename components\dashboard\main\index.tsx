/*eslint-disable*/
'use client';

import MainChart from '@/components/dashboard/main/cards/MainChart';
import MainDashboardTable from '@/components/dashboard/main/cards/MainDashboardTable';
import DataMetrics from '@/components/dashboard/main/cards/DataMetrics';
import RecentDatasets from '@/components/dashboard/main/cards/RecentDatasets';
import DashboardLayout from '@/components/layout';
import tableDataUserReports from '@/variables/tableDataUserReports';
import { User } from '@supabase/supabase-js';
interface Props {
  user: User | null | undefined;
  userDetails: { [x: string]: any } | null | any;
}

export default function DataDashboard(props: Props) {
  return (
    <DashboardLayout
      user={props.user}
      userDetails={props.userDetails}
      title="Oyu-Data-AI Dashboard"
      description="Mongolian Market Intelligence & Data Analytics Platform"
    >
      <div className="h-full w-full">
        {/* Welcome Section */}
        <div className="mb-8 rounded-lg bg-gradient-to-r from-oyu-blue-600 to-oyu-purple-600 p-6 text-white">
          <h1 className="text-3xl font-bold mb-2">Welcome to Oyu-Data-AI</h1>
          <p className="text-lg opacity-90">
            Your gateway to Mongolian market intelligence and data-driven insights
          </p>
        </div>

        {/* Key Metrics */}
        <DataMetrics />

        {/* Recent Datasets */}
        <div className="mb-8">
          <RecentDatasets />
        </div>

        {/* Analytics Charts */}
        <div className="mb-5 flex gap-5 flex-col xl:flex-row w-full">
          <MainChart />
        </div>

        {/* Data Tables and Insights */}
        <div className="h-full w-full rounded-lg">
          <MainDashboardTable tableData={tableDataUserReports} />
        </div>
      </div>
    </DashboardLayout>
  );
}
