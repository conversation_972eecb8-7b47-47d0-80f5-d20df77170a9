'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  HiOutlineDocumentText,
  HiOutlineClockIcon,
  HiOutlineCurrencyDollar,
  HiOutlineCheckCircle
} from 'react-icons/hi2';

interface ResearchRequest {
  title: string;
  description: string;
  category: string;
  urgency: string;
  budget: string;
  deliverables: string[];
  timeline: string;
  contactInfo: {
    name: string;
    email: string;
    company: string;
  };
}

const categories = [
  'Real Estate Analysis',
  'Market Entry Strategy',
  'Consumer Behavior Study',
  'Competitive Intelligence',
  'Demographic Research',
  'Industry Analysis',
  'Location Analysis',
  'Custom Data Collection'
];

const urgencyLevels = [
  { value: 'low', label: 'Standard (2-4 weeks)', price: 'Base price' },
  { value: 'medium', label: 'Priority (1-2 weeks)', price: '+25%' },
  { value: 'high', label: 'Rush (3-7 days)', price: '+50%' }
];

const budgetRanges = [
  '$500 - $1,000',
  '$1,000 - $2,500',
  '$2,500 - $5,000',
  '$5,000 - $10,000',
  '$10,000+'
];

const deliverableOptions = [
  'Executive Summary Report',
  'Raw Data (CSV/JSON)',
  'Data Visualization Dashboard',
  'Market Analysis Presentation',
  'Competitive Landscape Report',
  'Recommendations & Action Plan',
  'Follow-up Consultation Call'
];

export default function CustomResearchForm() {
  const [formData, setFormData] = useState<ResearchRequest>({
    title: '',
    description: '',
    category: '',
    urgency: '',
    budget: '',
    deliverables: [],
    timeline: '',
    contactInfo: {
      name: '',
      email: '',
      company: ''
    }
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleDeliverableChange = (deliverable: string, checked: boolean) => {
    if (checked) {
      setFormData(prev => ({
        ...prev,
        deliverables: [...prev.deliverables, deliverable]
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        deliverables: prev.deliverables.filter(d => d !== deliverable)
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setIsSubmitted(true);
  };

  if (isSubmitted) {
    return (
      <Card className="max-w-2xl mx-auto">
        <CardContent className="pt-6 text-center">
          <div className="mb-4">
            <HiOutlineCheckCircle className="h-16 w-16 text-green-500 mx-auto" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Request Submitted Successfully!</h2>
          <p className="text-gray-600 mb-6">
            Our research team will review your request and get back to you within 24 hours with a detailed proposal and timeline.
          </p>
          <div className="bg-blue-50 p-4 rounded-lg mb-6">
            <p className="text-sm text-blue-800">
              <strong>What's next?</strong><br />
              1. Our team will analyze your requirements<br />
              2. We'll prepare a detailed proposal with pricing<br />
              3. You'll receive a project timeline and milestones<br />
              4. Upon approval, research begins immediately
            </p>
          </div>
          <Button 
            onClick={() => {
              setIsSubmitted(false);
              setFormData({
                title: '',
                description: '',
                category: '',
                urgency: '',
                budget: '',
                deliverables: [],
                timeline: '',
                contactInfo: { name: '', email: '', company: '' }
              });
            }}
            variant="outline"
          >
            Submit Another Request
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Custom Research Request</h1>
        <p className="text-gray-600">
          Need specific market intelligence? Our expert analysts can create custom datasets and reports tailored to your business needs.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Project Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <HiOutlineDocumentText className="h-5 w-5 mr-2" />
              Project Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Project Title *
              </label>
              <Input
                required
                placeholder="e.g., Coffee Shop Market Analysis for Ulaanbaatar"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Research Category *
              </label>
              <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select research category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Detailed Description *
              </label>
              <Textarea
                required
                rows={4}
                placeholder="Describe your research needs, specific questions you want answered, target market, geographic scope, etc."
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>
          </CardContent>
        </Card>

        {/* Timeline & Budget */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <HiOutlineClockIcon className="h-5 w-5 mr-2" />
              Timeline & Budget
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Urgency Level *
              </label>
              <Select value={formData.urgency} onValueChange={(value) => setFormData(prev => ({ ...prev, urgency: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select urgency level" />
                </SelectTrigger>
                <SelectContent>
                  {urgencyLevels.map(level => (
                    <SelectItem key={level.value} value={level.value}>
                      <div className="flex items-center justify-between w-full">
                        <span>{level.label}</span>
                        <Badge variant="outline" className="ml-2">{level.price}</Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Budget Range *
              </label>
              <Select value={formData.budget} onValueChange={(value) => setFormData(prev => ({ ...prev, budget: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select budget range" />
                </SelectTrigger>
                <SelectContent>
                  {budgetRanges.map(range => (
                    <SelectItem key={range} value={range}>
                      {range}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Deliverables */}
        <Card>
          <CardHeader>
            <CardTitle>Expected Deliverables</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3 md:grid-cols-2">
              {deliverableOptions.map(deliverable => (
                <div key={deliverable} className="flex items-center space-x-2">
                  <Checkbox
                    id={deliverable}
                    checked={formData.deliverables.includes(deliverable)}
                    onCheckedChange={(checked) => handleDeliverableChange(deliverable, checked as boolean)}
                  />
                  <label htmlFor={deliverable} className="text-sm text-gray-700">
                    {deliverable}
                  </label>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name *
                </label>
                <Input
                  required
                  value={formData.contactInfo.name}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    contactInfo: { ...prev.contactInfo, name: e.target.value }
                  }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address *
                </label>
                <Input
                  required
                  type="email"
                  value={formData.contactInfo.email}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    contactInfo: { ...prev.contactInfo, email: e.target.value }
                  }))}
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Company/Organization
              </label>
              <Input
                value={formData.contactInfo.company}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  contactInfo: { ...prev.contactInfo, company: e.target.value }
                }))}
              />
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="text-center">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="w-full md:w-auto px-8 py-3 bg-gradient-to-r from-oyu-blue-600 to-oyu-purple-600 hover:from-oyu-blue-700 hover:to-oyu-purple-700"
          >
            {isSubmitting ? 'Submitting Request...' : 'Submit Research Request'}
          </Button>
          <p className="text-xs text-gray-500 mt-2">
            Our team will respond within 24 hours with a detailed proposal
          </p>
        </div>
      </form>
    </div>
  );
}
