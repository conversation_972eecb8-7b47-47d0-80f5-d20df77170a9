'use client';

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  HiOutlineChartBarSquare, 
  HiOutlineTableCells, 
  HiOutlineUsers, 
  HiOutlineCurrencyDollar 
} from 'react-icons/hi2';

interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative' | 'neutral';
  icon: React.ReactNode;
  description: string;
}

function MetricCard({ title, value, change, changeType, icon, description }: MetricCardProps) {
  const changeColor = {
    positive: 'text-green-600 bg-green-50',
    negative: 'text-red-600 bg-red-50',
    neutral: 'text-gray-600 bg-gray-50'
  };

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-600">{title}</CardTitle>
        <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-oyu-blue-500 to-oyu-purple-500 flex items-center justify-center text-white">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-gray-900">{value}</div>
        <div className="flex items-center justify-between mt-2">
          <Badge variant="outline" className={`${changeColor[changeType]} border-none`}>
            {change}
          </Badge>
        </div>
        <p className="text-xs text-gray-500 mt-2">{description}</p>
      </CardContent>
    </Card>
  );
}

export default function DataMetrics() {
  const metrics = [
    {
      title: 'Total Datasets',
      value: '2,847',
      change: '+12% this month',
      changeType: 'positive' as const,
      icon: <HiOutlineTableCells className="h-4 w-4" />,
      description: 'Curated datasets covering Mongolian market'
    },
    {
      title: 'Active Users',
      value: '1,234',
      change: '+8% this week',
      changeType: 'positive' as const,
      icon: <HiOutlineUsers className="h-4 w-4" />,
      description: 'Researchers and businesses using platform'
    },
    {
      title: 'Data Queries',
      value: '15,678',
      change: '+23% today',
      changeType: 'positive' as const,
      icon: <HiOutlineChartBarSquare className="h-4 w-4" />,
      description: 'AI-powered data queries processed'
    },
    {
      title: 'Market Value',
      value: '₮2.4B',
      change: '****% this quarter',
      changeType: 'positive' as const,
      icon: <HiOutlineCurrencyDollar className="h-4 w-4" />,
      description: 'Total market data value tracked'
    }
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
      {metrics.map((metric, index) => (
        <MetricCard key={index} {...metric} />
      ))}
    </div>
  );
}
