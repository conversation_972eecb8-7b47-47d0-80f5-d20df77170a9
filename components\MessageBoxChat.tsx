import { Card } from '@/components/ui/card';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';

export default function MessageBox(props: { output: string }) {
  const { output } = props;

  return (
    <Card
      className={`${
        output ? 'flex' : 'hidden'
      } !max-h-max bg-zinc-950 p-5 !px-[22px] !py-[22px] text-base font-normal leading-6 text-white backdrop-blur-xl dark:border-zinc-800 dark:!bg-white/5 dark:text-white md:text-base md:leading-[26px]`}
    >
      <ReactMarkdown
        className="text-base font-normal w-full"
        components={{
          code({ node, inline, className, children, ...props }) {
            const match = /language-(\w+)/.exec(className || '');
            const isCodeBlock = !inline && match;

            if (isCodeBlock) {
              return (
                <SyntaxHighlighter
                  style={oneDark}
                  language={match[1]}
                  PreTag="div"
                  className="rounded-lg !bg-zinc-800 !text-sm"
                  {...props}
                >
                  {String(children).replace(/\n$/, '')}
                </SyntaxHighlighter>
              );
            }

            // For inline code or dashboard ASCII art
            return (
              <code
                className={`${
                  className?.includes('dashboard') || String(children).includes('█') || String(children).includes('│') || String(children).includes('┌')
                    ? 'block whitespace-pre font-mono text-sm bg-zinc-800 p-3 rounded-lg border border-zinc-700 overflow-x-auto'
                    : 'bg-zinc-800 px-1.5 py-0.5 rounded text-sm'
                } text-green-400`}
                {...props}
              >
                {children}
              </code>
            );
          },
          pre({ children }) {
            return <div className="overflow-x-auto">{children}</div>;
          },
          h1({ children }) {
            return <h1 className="text-2xl font-bold mb-4 text-blue-400">{children}</h1>;
          },
          h2({ children }) {
            return <h2 className="text-xl font-semibold mb-3 text-blue-300">{children}</h2>;
          },
          h3({ children }) {
            return <h3 className="text-lg font-medium mb-2 text-blue-200">{children}</h3>;
          },
          ul({ children }) {
            return <ul className="list-disc list-inside mb-4 space-y-1">{children}</ul>;
          },
          ol({ children }) {
            return <ol className="list-decimal list-inside mb-4 space-y-1">{children}</ol>;
          },
          li({ children }) {
            return <li className="text-gray-200">{children}</li>;
          },
          p({ children }) {
            return <p className="mb-3 text-gray-100 leading-relaxed">{children}</p>;
          },
          strong({ children }) {
            return <strong className="font-semibold text-yellow-400">{children}</strong>;
          },
          em({ children }) {
            return <em className="italic text-blue-300">{children}</em>;
          },
          blockquote({ children }) {
            return (
              <blockquote className="border-l-4 border-blue-500 pl-4 py-2 mb-4 bg-zinc-800/50 rounded-r-lg">
                {children}
              </blockquote>
            );
          },
          table({ children }) {
            return (
              <div className="overflow-x-auto mb-4">
                <table className="min-w-full border border-zinc-700 rounded-lg">
                  {children}
                </table>
              </div>
            );
          },
          thead({ children }) {
            return <thead className="bg-zinc-800">{children}</thead>;
          },
          tbody({ children }) {
            return <tbody className="bg-zinc-900/50">{children}</tbody>;
          },
          tr({ children }) {
            return <tr className="border-b border-zinc-700">{children}</tr>;
          },
          th({ children }) {
            return <th className="px-4 py-2 text-left font-semibold text-blue-300">{children}</th>;
          },
          td({ children }) {
            return <td className="px-4 py-2 text-gray-200">{children}</td>;
          }
        }}
      >
        {output ? output : ''}
      </ReactMarkdown>
    </Card>
  );
}
