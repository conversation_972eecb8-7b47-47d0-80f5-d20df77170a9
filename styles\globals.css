@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --radius: 10px;
  --chart: #0F172A;
  --background: 0 0% 100%;
  --foreground: 240, 10%, 4%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --card: 0, 0%, 100%;
  --card-foreground: 0, 0%, 100%;
  --popover: 0, 0%, 100%;
  --popover-foreground: 0, 0%, 100%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --primary: 140 30% 55%; 
  /* ^ this is green */
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 240, 10%, 4%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 240, 10%, 4%;
  --destructive: 0 100% 50%;
  --destructive-foreground: 210 40% 98%;
  --ring: transparent;
  --chart-1: #52525b;
  --chart-2: #D4D4D8;
  --chart-3: #030712;
  --chart-4: #71717a;
  --chart-5: #a1a1aa;
}

:root.dark {
  --background: 240 10% 4%;
  --foreground: 0, 0%, 100%;
  --chart: 0, 0%, 100%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --card: 240 10% 4%;
  --card-foreground: 0 0% 100%;
  --popover: 240 10% 4%;
  --popover-foreground: 0 0% 100%;
  --border: 240 4% 16%;
  --input: 240 4% 16%;
  --primary: 0 0% 100%;
  --primary-foreground: 240 10% 4%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --accent: 240 4% 16%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 100% 50%;
  --destructive-foreground: 210 40% 98%;
  --ring: 0 0% 0%;
  --chart-1: #ffffff;
  --chart-2: #71717A;
  --chart-3: #030712;
  --chart-4: #71717a;
  --chart-5: #a1a1aa;
}

body {
  font-family: 'Inter', sans-serif;
}

html {
  scroll-behavior: smooth;
  font-family: 'Inter', sans-serif;
  color-scheme: unset !important;
}

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  color: hsl(var(--foreground));
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

option {
  color: black;
}

p {
  letter-spacing: 0px;
}

img {
  pointer-events: none;
}
::-moz-selection {
  /* Code for Firefox */
  color: white;
  background: #09090B;
}

::selection {
  color: white;
  background: #09090B;
}

input.defaultCheckbox {
  color: white;
}

input.defaultCheckbox::before {
  content: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6.66662 10.115L12.7946 3.98633L13.7379 4.92899L6.66662 12.0003L2.42395 7.75766L3.36662 6.81499L6.66662 10.115Z' fill='white'/%3E%3C/svg%3E%0A");
  fill: currentColor;
  opacity: 0;
  height: 16px;
  width: 16px;
  top: 0;
  position: absolute;
  left: 50%;
  transform: translate(-50%, 0px);
}

input.defaultCheckbox::before path {
  fill: currentColor;
}

input:checked.defaultCheckbox::before {
  opacity: 1;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
      }
  .dark {
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}